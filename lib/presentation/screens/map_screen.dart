import 'package:flutter/material.dart';
import 'package:google_maps_flutter/google_maps_flutter.dart';
import 'package:provider/provider.dart';
import 'package:geolocator/geolocator.dart';
import 'dart:ui' as ui;
import 'dart:typed_data';

import '../../data/models/api_villa_model.dart';
import '../../data/providers/villa_provider.dart';

import '../../routes.dart';
import '../../core/utils/location_service.dart';
import '../../core/utils/service_locator.dart';
import '../widgets/filter_bottom_sheet.dart';

class MapScreen extends StatefulWidget {
  const MapScreen({super.key});

  @override
  State<MapScreen> createState() => _MapScreenState();
}

class _MapScreenState extends State<MapScreen> {
  GoogleMapController? _mapController;
  final Set<Marker> _markers = {};
  final TextEditingController _searchController = TextEditingController();
  bool _hasMapError = false;
  bool _isLoadingLocation = true;
  bool _isSearchActive = false;
  Brightness? _lastBrightness;

  // Initial camera position - will be updated with user's location
  CameraPosition _initialCameraPosition = const CameraPosition(
    target: LatLng(
      19.281112,
      73.047047,
    ), // Default to Mumbai (matches LocationService)
    zoom: 12,
  );

  @override
  void initState() {
    super.initState();
    _initializeLocation();

    // Defer villa fetching until after the build is complete
    WidgetsBinding.instance.addPostFrameCallback((_) {
      _fetchVillas();
    });
  }

  Future<void> _initializeLocation() async {
    try {
      // Get user's current location
      Position position = await LocationService.getCachedOrCurrentLocation();

      // Update initial camera position with user's location
      setState(() {
        _initialCameraPosition = CameraPosition(
          target: LatLng(position.latitude, position.longitude),
          zoom: 12,
        );
        _isLoadingLocation = false;
      });

      // Move camera to user's location if map is already created
      if (_mapController != null) {
        _mapController!.animateCamera(
          CameraUpdate.newLatLng(LatLng(position.latitude, position.longitude)),
        );
      }

      // Villa markers will be added after API call in _fetchVillas
    } catch (e) {
      // If location fails, use default position
      setState(() {
        _isLoadingLocation = false;
      });
      // Villa markers will be added after API call in _fetchVillas
    }
  }

  Future<void> _fetchVillas() async {
    try {
      final villaProvider = Provider.of<VillaProvider>(context, listen: false);
      await villaProvider.fetchAllVillas();

      // Add villa markers after data is loaded
      if (mounted) {
        _addVillaMarkers();
      }
    } catch (e) {
      // Handle error silently or show error message
      sl.logger.e('Error fetching villas: $e');
    }
  }

  @override
  void dispose() {
    _mapController?.dispose();
    _searchController.dispose();
    super.dispose();
  }

  // Create simple price-only marker - theme-aware
  Future<BitmapDescriptor> _createPriceMarker(ApiVilla villa) async {
    const double markerWidth = 80;
    const double markerHeight = 32;

    final ui.PictureRecorder pictureRecorder = ui.PictureRecorder();
    final Canvas canvas = Canvas(pictureRecorder);

    // Get theme colors
    final theme = Theme.of(context);
    final isDarkMode = theme.brightness == Brightness.dark;

    // Draw shadow
    final Paint shadowPaint =
        Paint()
          ..color = Colors.black.withValues(alpha: 0.2)
          ..maskFilter = const ui.MaskFilter.blur(ui.BlurStyle.normal, 3);
    final RRect shadowRect = RRect.fromRectAndRadius(
      Rect.fromLTWH(2, 2, markerWidth - 4, markerHeight - 4),
      const Radius.circular(16),
    );
    canvas.drawRRect(shadowRect, shadowPaint);

    // Main container background
    final Paint backgroundPaint =
        Paint()
          ..color = isDarkMode ? const Color(0xFF1E1E1E) : Colors.white
          ..style = PaintingStyle.fill;

    // Main container
    final RRect containerRect = RRect.fromRectAndRadius(
      Rect.fromLTWH(0, 0, markerWidth, markerHeight),
      const Radius.circular(16),
    );
    canvas.drawRRect(containerRect, backgroundPaint);

    // Border
    final Paint borderPaint =
        Paint()
          ..color = theme.colorScheme.primary
          ..style = PaintingStyle.stroke
          ..strokeWidth = 2.0;
    canvas.drawRRect(containerRect, borderPaint);

    // Price text
    final String priceText = '₹${villa.effectiveWeekdayPrice.toInt()}';
    final TextStyle priceStyle = TextStyle(
      color: theme.colorScheme.primary,
      fontSize: 12,
      fontWeight: FontWeight.bold,
    );

    final TextPainter pricePainter = TextPainter(
      text: TextSpan(text: priceText, style: priceStyle),
      textDirection: TextDirection.ltr,
    );
    pricePainter.layout();

    // Center the text
    final double textX = (markerWidth - pricePainter.width) / 2;
    final double textY = (markerHeight - pricePainter.height) / 2;
    pricePainter.paint(canvas, Offset(textX, textY));

    final ui.Picture picture = pictureRecorder.endRecording();
    final ui.Image image = await picture.toImage(
      markerWidth.toInt(),
      markerHeight.toInt(),
    );
    final ByteData? byteData = await image.toByteData(
      format: ui.ImageByteFormat.png,
    );

    return BitmapDescriptor.bytes(byteData!.buffer.asUint8List());
  }

  Future<void> _addVillaMarkers() async {
    final villaProvider = Provider.of<VillaProvider>(context, listen: false);

    // Use map search results if available, otherwise use all villas
    final apiVillas =
        _isSearchActive && villaProvider.mapSearchResults.isNotEmpty
            ? villaProvider.mapSearchResults
            : villaProvider.apiVillas;

    final newMarkers = <Marker>{};

    for (var villa in apiVillas) {
      // Check if villa has valid coordinates
      final latitude = villa.latitude;
      final longitude = villa.longitude;

      if (latitude != null && longitude != null) {
        // Create simple price marker with current theme
        final BitmapDescriptor customIcon = await _createPriceMarker(villa);

        newMarkers.add(
          Marker(
            markerId: MarkerId(villa.id.toString()),
            position: LatLng(latitude, longitude),
            icon: customIcon,
            infoWindow: InfoWindow(
              title: villa.name,
              snippet:
                  '₹${villa.effectiveWeekdayPrice.toInt()}/night • ${villa.rating}★',
              onTap: () async {
                await _navigateToVillaDetail(villa.id.toString());
              },
            ),
            onTap: () {
              _showVillaBottomSheet(villa);
            },
            anchor: const Offset(
              0.5,
              1.0,
            ), // Position marker correctly with custom icon
          ),
        );
      }
    }

    if (mounted) {
      setState(() {
        _markers.clear();
        _markers.addAll(newMarkers);
      });

      // If no markers are visible in the current view, zoom out to show all
      if (newMarkers.isNotEmpty && _mapController != null) {
        LatLngBounds bounds = _boundsFromMarkers(newMarkers);
        _mapController!.animateCamera(
          CameraUpdate.newLatLngBounds(bounds, 100.0), // Add padding
        );
      }
    }
  }

  // Helper to calculate LatLngBounds from a set of markers
  LatLngBounds _boundsFromMarkers(Set<Marker> markers) {
    double? minLat, maxLat, minLng, maxLng;

    for (var marker in markers) {
      if (minLat == null || marker.position.latitude < minLat) {
        minLat = marker.position.latitude;
      }
      if (maxLat == null || marker.position.latitude > maxLat) {
        maxLat = marker.position.latitude;
      }
      if (minLng == null || marker.position.longitude < minLng) {
        minLng = marker.position.longitude;
      }
      if (maxLng == null || marker.position.longitude > maxLng) {
        maxLng = marker.position.longitude;
      }
    }

    return LatLngBounds(
      southwest: LatLng(minLat!, minLng!),
      northeast: LatLng(maxLat!, maxLng!),
    );
  }

  Future<void> _performSearch(String searchTerm) async {
    if (searchTerm.trim().isEmpty) {
      // If search is empty, clear search results and reset to show all villas
      setState(() {
        _isSearchActive = false;
      });
      final villaProvider = Provider.of<VillaProvider>(context, listen: false);
      villaProvider
          .clearMapSearchResults(); // Clear the separate search results
      await _addVillaMarkers(); // Refresh markers with all villas
      return;
    }

    setState(() {
      _isSearchActive = true;
    });

    try {
      final villaProvider = Provider.of<VillaProvider>(context, listen: false);

      // Show loading indicator
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('Searching villas...'),
          duration: Duration(seconds: 1),
        ),
      );

      // Filter villas locally based on search term from existing data
      final allVillas = villaProvider.apiVillas;
      final filteredVillas =
          allVillas.where((villa) {
            final searchLower = searchTerm.toLowerCase();
            return villa.name.toLowerCase().contains(searchLower) ||
                villa.address.toLowerCase().contains(searchLower) ||
                villa.area.toLowerCase().contains(searchLower) ||
                villa.villaGroupName.toLowerCase().contains(searchLower);
          }).toList();

      // Store filtered results in the separate map search results
      // Use the proper method to set search results
      villaProvider.setMapSearchResults(filteredVillas);

      // Refresh markers with search results
      await _addVillaMarkers();

      // Move camera to first result if available
      if (filteredVillas.isNotEmpty && _mapController != null) {
        final firstVilla = filteredVillas.first;
        final lat = firstVilla.latitude;
        final lng = firstVilla.longitude;
        if (lat != null && lng != null) {
          _mapController!.animateCamera(
            CameraUpdate.newLatLngZoom(LatLng(lat, lng), 14),
          );
        }
      }

      // Show results message
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Found ${filteredVillas.length} villa(s)'),
            duration: const Duration(seconds: 2),
          ),
        );
      }
    } catch (e) {
      sl.logger.e('Error searching villas: $e');
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: const Text('Search failed. Please try again.'),
            backgroundColor: Theme.of(context).colorScheme.error,
          ),
        );
      }
    }
  }

  Future<void> _navigateToVillaDetail(String villaId) async {
    final villaProvider = Provider.of<VillaProvider>(context, listen: false);

    // Use the new helper method to ensure villa data is available
    final dataAvailable = await villaProvider.ensureVillaDataById(villaId);

    if (!dataAvailable) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: const Text(
              'Villa details not available. Please try again.',
            ),
            backgroundColor: Theme.of(context).colorScheme.error,
          ),
        );
      }
      return;
    }

    // Get villa data and use smart navigation
    final villa = villaProvider.getVillaByIdExtended(villaId);
    if (villa != null && mounted) {
      AppRoutes.navigateToVillaOrGroup(context, villa);
    }
  }

  void _showVillaBottomSheet(ApiVilla villa) {
    if (mounted) {
      showModalBottomSheet(
        context: context,
        isScrollControlled: true,
        backgroundColor: Colors.transparent,
        builder: (context) => _buildVillaBottomSheet(villa),
      );
    }
  }

  Widget _buildPersistentBottomSheet() {
    final theme = Theme.of(context);
    final villaProvider = Provider.of<VillaProvider>(context);

    // Use map search results if available, otherwise use all villas
    final apiVillas =
        _isSearchActive && villaProvider.mapSearchResults.isNotEmpty
            ? villaProvider.mapSearchResults
            : villaProvider.apiVillas;

    return DraggableScrollableSheet(
      initialChildSize: 0.3, // Start at 30% of screen height
      minChildSize: 0.15, // Minimum 15% of screen height
      maxChildSize: 0.8, // Maximum 80% of screen height
      builder: (context, scrollController) {
        return Container(
          decoration: BoxDecoration(
            color: theme.scaffoldBackgroundColor,
            borderRadius: const BorderRadius.only(
              topLeft: Radius.circular(20),
              topRight: Radius.circular(20),
            ),
            boxShadow: [
              BoxShadow(
                color: Colors.black.withValues(alpha: 0.1),
                blurRadius: 10,
                offset: const Offset(0, -5),
              ),
            ],
          ),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Handle bar
              Center(
                child: Container(
                  margin: const EdgeInsets.symmetric(vertical: 12.0),
                  width: 40,
                  height: 4,
                  decoration: BoxDecoration(
                    color: theme.colorScheme.outline.withValues(alpha: 0.3),
                    borderRadius: BorderRadius.circular(2),
                  ),
                ),
              ),

              // Villa list
              Expanded(
                child:
                    apiVillas.isEmpty
                        ? Center(
                          child: Text(
                            'No villas found',
                            style: theme.textTheme.bodyMedium?.copyWith(
                              color: theme.textTheme.bodyMedium?.color
                                  ?.withValues(alpha: 0.7),
                            ),
                          ),
                        )
                        : ListView.builder(
                          controller: scrollController,
                          padding: const EdgeInsets.symmetric(horizontal: 16),
                          itemCount: apiVillas.length,
                          itemBuilder: (context, index) {
                            final villa = apiVillas[index];
                            return _buildVillaCard(villa);
                          },
                        ),
              ),
            ],
          ),
        );
      },
    );
  }

  Widget _buildVillaCard(ApiVilla villa) {
    final theme = Theme.of(context);
    return Container(
      margin: const EdgeInsets.only(bottom: 12),
      decoration: BoxDecoration(
        color: theme.cardColor,
        borderRadius: BorderRadius.circular(12),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.05),
            blurRadius: 8,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Row(
        children: [
          // Villa image
          ClipRRect(
            borderRadius: const BorderRadius.only(
              topLeft: Radius.circular(12),
              bottomLeft: Radius.circular(12),
            ),
            child: SizedBox(
              width: 100,
              height: 80,
              child:
                  villa.primaryImage.isNotEmpty
                      ? Image.network(
                        villa.primaryImage,
                        fit: BoxFit.cover,
                        errorBuilder: (context, error, stackTrace) {
                          return _buildPlaceholderImage();
                        },
                      )
                      : _buildPlaceholderImage(),
            ),
          ),

          // Villa details
          Expanded(
            child: Padding(
              padding: const EdgeInsets.all(12),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        villa.name,
                        style: theme.textTheme.titleSmall?.copyWith(
                          fontWeight: FontWeight.bold,
                        ),
                        maxLines: 1,
                        overflow: TextOverflow.ellipsis,
                      ),
                      const SizedBox(height: 2),
                      Row(
                        children: [
                          Icon(
                            Icons.star_rounded,
                            color: Colors.amber.shade700,
                            size: 14,
                          ),
                          const SizedBox(width: 2),
                          Text(
                            '${villa.rating}',
                            style: theme.textTheme.bodySmall?.copyWith(
                              fontWeight: FontWeight.w500,
                            ),
                          ),
                          const SizedBox(width: 4),
                          Text(
                            '• 786 Reviews',
                            style: theme.textTheme.bodySmall?.copyWith(
                              color: theme.textTheme.bodyMedium?.color
                                  ?.withValues(alpha: 0.7),
                            ),
                          ),
                        ],
                      ),
                    ],
                  ),
                  Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      Text(
                        '₹${villa.effectiveWeekdayPrice.toInt()}',
                        style: theme.textTheme.titleSmall?.copyWith(
                          fontWeight: FontWeight.bold,
                          color: theme.colorScheme.primary,
                        ),
                      ),
                      GestureDetector(
                        onTap: () async {
                          await _navigateToVillaDetail(villa.id.toString());
                        },
                        child: Container(
                          padding: const EdgeInsets.symmetric(
                            horizontal: 8,
                            vertical: 4,
                          ),
                          decoration: BoxDecoration(
                            color: theme.colorScheme.primary,
                            borderRadius: BorderRadius.circular(6),
                          ),
                          child: Icon(
                            Icons.arrow_forward,
                            color: theme.colorScheme.onPrimary,
                            size: 16,
                          ),
                        ),
                      ),
                    ],
                  ),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildPlaceholderImage() {
    return Container(
      color: Theme.of(context).colorScheme.surfaceContainerHighest,
      child: Center(
        child: Icon(
          Icons.villa,
          size: 32,
          color: Theme.of(context).iconTheme.color?.withValues(alpha: 0.5),
        ),
      ),
    );
  }

  Widget _buildVillaBottomSheet(ApiVilla villa) {
    final theme = Theme.of(context);
    return Container(
      decoration: BoxDecoration(
        color: theme.scaffoldBackgroundColor,
        borderRadius: const BorderRadius.only(
          topLeft: Radius.circular(20), // Match project standard
          topRight: Radius.circular(20), // Match project standard
        ),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.1),
            blurRadius: 10,
            offset: const Offset(0, -5),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        mainAxisSize: MainAxisSize.min,
        children: [
          // Handle bar - matching project pattern
          Center(
            child: Container(
              margin: const EdgeInsets.symmetric(vertical: 12.0),
              width: 40,
              height: 4,
              decoration: BoxDecoration(
                color: theme.colorScheme.outline.withValues(alpha: 0.3),
                borderRadius: BorderRadius.circular(2),
              ),
            ),
          ),

          // Villa image
          Padding(
            padding: const EdgeInsets.fromLTRB(
              16,
              0,
              16,
              16,
            ), // Standardized padding
            child: ClipRRect(
              borderRadius: BorderRadius.circular(12), // Standardized radius
              child: AspectRatio(
                aspectRatio: 16 / 9,
                child:
                    villa.primaryImage.isNotEmpty
                        ? Image.network(
                          villa.primaryImage,
                          width: double.infinity,
                          fit: BoxFit.cover,
                          errorBuilder: (context, error, stackTrace) {
                            return _buildBottomSheetPlaceholderImage();
                          },
                        )
                        : _buildBottomSheetPlaceholderImage(),
              ),
            ),
          ),

          // Villa details
          SingleChildScrollView(
            padding: const EdgeInsets.all(16.0), // Standardized padding
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              mainAxisSize: MainAxisSize.min,
              children: [
                // Villa name and rating
                Row(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Expanded(
                      child: Text(
                        villa.name,
                        style: theme.textTheme.titleLarge?.copyWith(
                          fontWeight: FontWeight.bold,
                          // color: theme.colorScheme.onSurface, // Default
                        ),
                        maxLines: 2,
                        overflow: TextOverflow.ellipsis,
                      ),
                    ),
                    const SizedBox(width: 8),
                    Container(
                      padding: const EdgeInsets.symmetric(
                        horizontal: 8, // Standardized padding
                        vertical: 4, // Standardized padding
                      ),
                      decoration: BoxDecoration(
                        color: Colors.amber.withValues(
                          alpha: 0.15,
                        ), // Keep semantic amber
                        borderRadius: BorderRadius.circular(
                          8,
                        ), // Standardized radius
                      ),
                      child: Row(
                        mainAxisSize: MainAxisSize.min,
                        children: [
                          Icon(
                            Icons.star_rounded,
                            color: Colors.amber.shade700, // Keep semantic amber
                            size: 16, // Standardized size
                          ),
                          const SizedBox(width: 4),
                          Text(
                            villa.rating.toString(),
                            style: theme.textTheme.labelLarge?.copyWith(
                              fontWeight: FontWeight.bold,
                              color:
                                  Colors.amber.shade800, // Keep semantic amber
                            ),
                          ),
                        ],
                      ),
                    ),
                  ],
                ),

                const SizedBox(height: 8), // Standardized spacing
                // Address
                Row(
                  children: [
                    Icon(
                      Icons.location_on_outlined, // Using outlined version
                      color: theme.iconTheme.color?.withValues(alpha: 0.7),
                      size: 18, // Standardized size
                    ),
                    const SizedBox(width: 8),
                    Expanded(
                      child: Text(
                        villa.address.isNotEmpty ? villa.address : villa.area,
                        style: theme.textTheme.bodyMedium?.copyWith(
                          color: theme.textTheme.bodyMedium?.color?.withValues(
                            alpha: 0.7,
                          ),
                          fontSize: 14,
                        ),
                        maxLines: 2,
                        overflow: TextOverflow.ellipsis,
                      ),
                    ),
                  ],
                ),

                const SizedBox(height: 12), // Standardized spacing
                // Price
                Row(
                  children: [
                    Container(
                      padding: const EdgeInsets.symmetric(
                        horizontal: 10,
                        vertical: 5,
                      ),
                      decoration: BoxDecoration(
                        color: Colors.green.withValues(
                          alpha: 0.1,
                        ), // Keep semantic green
                        borderRadius: BorderRadius.circular(8),
                      ),
                      child: Text(
                        '₹${villa.effectiveWeekdayPrice.toInt()}',
                        style: theme.textTheme.titleLarge?.copyWith(
                          fontWeight: FontWeight.bold,
                          color: Colors.green.shade700, // Keep semantic green
                        ),
                      ),
                    ),
                    const SizedBox(width: 8),
                    Text(
                      '/night',
                      style: theme.textTheme.bodySmall?.copyWith(
                        color: theme.textTheme.bodyMedium?.color?.withValues(
                          alpha: 0.7,
                        ),
                      ),
                    ),
                  ],
                ),

                const SizedBox(height: 16), // Standardized spacing
                // Villa features
                Wrap(
                  spacing: 8, // Standardized spacing
                  runSpacing: 8, // Standardized spacing
                  children: [
                    if (villa.noOfRoom > 0)
                      _buildFeatureChip(
                        Icons.bed_outlined,
                        '${villa.noOfRoom} Rooms',
                        theme,
                      ),
                    if (villa.noOfwashroom > 0)
                      _buildFeatureChip(
                        Icons.bathtub_outlined,
                        '${villa.noOfwashroom} Bathrooms',
                        theme,
                      ),
                    if (villa.noOfMemberTo > 0)
                      _buildFeatureChip(
                        Icons.people_alt_outlined,
                        'Up to ${villa.noOfMemberTo} guests',
                        theme,
                      ),
                    if (villa.isSwimmingPool)
                      _buildFeatureChip(Icons.pool_outlined, 'Pool', theme),
                    if (villa.wifi)
                      _buildFeatureChip(Icons.wifi_outlined, 'WiFi', theme),
                    if (villa.ac)
                      _buildFeatureChip(Icons.ac_unit_outlined, 'AC', theme),
                    if (villa.parking)
                      _buildFeatureChip(
                        Icons.local_parking_outlined,
                        'Parking',
                        theme,
                      ),
                  ],
                ),

                const SizedBox(height: 20), // Standardized spacing
                // Action buttons
                Row(
                  children: [
                    Expanded(
                      child: OutlinedButton(
                        onPressed: () {
                          Navigator.pop(context);
                        },
                        style: OutlinedButton.styleFrom(
                          foregroundColor: theme.colorScheme.primary,
                          padding: const EdgeInsets.symmetric(vertical: 12),
                          shape: RoundedRectangleBorder(
                            borderRadius: BorderRadius.circular(
                              8,
                            ), // Match villa_detail
                          ),
                          side: BorderSide(
                            color: theme.colorScheme.primary,
                            width: 1,
                          ),
                          textStyle: theme.textTheme.labelLarge?.copyWith(
                            fontWeight: FontWeight.w600,
                          ),
                        ),
                        child: const Text('Close'),
                      ),
                    ),
                    const SizedBox(width: 12), // Standardized spacing
                    Expanded(
                      flex: 2,
                      child: ElevatedButton(
                        onPressed: () async {
                          Navigator.pop(context);
                          await _navigateToVillaDetail(villa.id.toString());
                        },
                        style: ElevatedButton.styleFrom(
                          backgroundColor: theme.colorScheme.primary,
                          foregroundColor: theme.colorScheme.onPrimary,
                          padding: const EdgeInsets.symmetric(vertical: 12),
                          shape: RoundedRectangleBorder(
                            borderRadius: BorderRadius.circular(
                              8,
                            ), // Match villa_detail
                          ),
                          elevation: 2, // Subtle elevation
                          shadowColor: theme.colorScheme.primary.withValues(
                            alpha: 0.2,
                          ),
                          textStyle: theme.textTheme.labelLarge?.copyWith(
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                        child: const Text('View Details'),
                      ),
                    ),
                  ],
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildFeatureChip(IconData icon, String label, ThemeData theme) {
    return Container(
      padding: const EdgeInsets.symmetric(
        horizontal: 8,
        vertical: 4,
      ), // Standardized padding
      decoration: BoxDecoration(
        color: Theme.of(context).colorScheme.surfaceContainerHighest,
        borderRadius: BorderRadius.circular(16), // Pill shape
      ),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          Icon(
            icon,
            size: 16, // Standardized size
            color: theme.colorScheme.onSurfaceVariant, // Muted color
          ),
          const SizedBox(width: 6),
          Text(
            label,
            style: theme.textTheme.bodySmall?.copyWith(
              // Standardized style
              color: theme.colorScheme.onSurfaceVariant, // Muted color
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildBottomSheetPlaceholderImage() {
    return Container(
      width: double.infinity,
      color: Theme.of(context).colorScheme.surfaceContainerHighest,
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.villa,
            size: 48,
            color: Theme.of(context).iconTheme.color?.withValues(alpha: 0.5),
          ),
          const SizedBox(height: 8),
          Text(
            'No Image',
            style: TextStyle(
              color: Theme.of(
                context,
              ).textTheme.bodyMedium?.color?.withValues(alpha: 0.7),
              fontSize: 14,
              fontWeight: FontWeight.w500,
            ),
          ),
        ],
      ),
    );
  }

  void _showFilterBottomSheet() {
    if (mounted) {
      showModalBottomSheet(
        context: context,
        isScrollControlled: true,
        backgroundColor: Colors.transparent,
        builder:
            (context) => FilterBottomSheet(
              onFiltersApplied: () {
                // Refresh markers after filters are applied
                _addVillaMarkers();
              },
            ),
      );
    }
  }

  @override
  Widget build(BuildContext context) {
    // Check if theme has changed and refresh markers if needed
    final currentBrightness = Theme.of(context).brightness;
    if (_lastBrightness != null &&
        _lastBrightness != currentBrightness &&
        _markers.isNotEmpty) {
      // Theme changed, refresh markers with new theme
      WidgetsBinding.instance.addPostFrameCallback((_) {
        if (mounted) {
          _addVillaMarkers();
        }
      });
    }
    _lastBrightness = currentBrightness;

    return Scaffold(
      body: Column(
        children: [
          // Search bar - matching project style
          SafeArea(
            child: Padding(
              padding: const EdgeInsets.fromLTRB(16, 16, 16, 8),
              child: Row(
                children: [
                  Expanded(
                    child: Container(
                      padding: const EdgeInsets.symmetric(horizontal: 16),
                      decoration: BoxDecoration(
                        color:
                            Theme.of(
                              context,
                            ).colorScheme.surfaceContainerHighest,
                        borderRadius: BorderRadius.circular(30),
                      ),
                      child: TextField(
                        controller: _searchController,
                        style: TextStyle(
                          color: Theme.of(context).textTheme.bodyLarge?.color,
                        ),
                        decoration: InputDecoration(
                          hintText: 'Search name, city, or everything...',
                          hintStyle: TextStyle(
                            color: Theme.of(context).textTheme.bodyMedium?.color
                                ?.withValues(alpha: 0.6),
                          ),
                          border: InputBorder.none,
                          icon: Icon(
                            Icons.search,
                            color: Theme.of(
                              context,
                            ).iconTheme.color?.withValues(alpha: 0.7),
                          ),
                          suffixIcon:
                              _isSearchActive
                                  ? IconButton(
                                    icon: Icon(
                                      Icons.clear,
                                      color: Theme.of(
                                        context,
                                      ).iconTheme.color?.withValues(alpha: 0.7),
                                    ),
                                    onPressed: () {
                                      _searchController.clear();
                                      _performSearch('');
                                    },
                                    constraints: const BoxConstraints(
                                      minWidth: 20,
                                      minHeight: 20,
                                    ),
                                  )
                                  : null,
                          contentPadding: const EdgeInsets.symmetric(
                            vertical: 12,
                          ),
                          isDense: true,
                        ),
                        onSubmitted: (value) {
                          _performSearch(value);
                        },
                        textInputAction: TextInputAction.search,
                      ),
                    ),
                  ),
                  const SizedBox(width: 8),
                  Container(
                    decoration: BoxDecoration(
                      color: Theme.of(context).colorScheme.primary,
                      borderRadius: BorderRadius.circular(12),
                    ),
                    child: IconButton(
                      icon: Icon(
                        Icons.search,
                        color: Theme.of(context).colorScheme.onPrimary,
                      ),
                      onPressed: () {
                        _performSearch(_searchController.text);
                      },
                      constraints: const BoxConstraints(
                        minHeight: 40,
                        minWidth: 40,
                      ),
                      padding: EdgeInsets.zero,
                    ),
                  ),
                  const SizedBox(width: 8),
                  Container(
                    decoration: BoxDecoration(
                      color:
                          Theme.of(context).colorScheme.surfaceContainerHighest,
                      borderRadius: BorderRadius.circular(12),
                    ),
                    child: IconButton(
                      icon: Icon(
                        Icons.filter_list,
                        color: Theme.of(context).iconTheme.color,
                      ),
                      onPressed: () {
                        _showFilterBottomSheet();
                      },
                      constraints: const BoxConstraints(
                        minHeight: 40,
                        minWidth: 40,
                      ),
                      padding: EdgeInsets.zero,
                    ),
                  ),
                ],
              ),
            ),
          ),

          // Map view with bottom sheet
          Expanded(
            child: Stack(
              children: [
                // Fallback UI in case of map error
                Container(
                  color: Theme.of(context).colorScheme.surfaceContainerHighest,
                  child: Center(
                    child:
                        _hasMapError
                            ? Column(
                              mainAxisAlignment: MainAxisAlignment.center,
                              mainAxisSize: MainAxisSize.min,
                              children: [
                                Icon(
                                  Icons.map_outlined,
                                  size: 64,
                                  color: Theme.of(
                                    context,
                                  ).iconTheme.color?.withValues(alpha: 0.5),
                                ),
                                const SizedBox(height: 16),
                                Text(
                                  'Unable to load map',
                                  style: TextStyle(
                                    fontSize: 18,
                                    fontWeight: FontWeight.bold,
                                    color:
                                        Theme.of(
                                          context,
                                        ).textTheme.headlineSmall?.color,
                                  ),
                                  textAlign: TextAlign.center,
                                ),
                                const SizedBox(height: 8),
                                Text(
                                  'Please check your internet connection',
                                  style: TextStyle(
                                    color: Theme.of(context)
                                        .textTheme
                                        .bodyMedium
                                        ?.color
                                        ?.withValues(alpha: 0.7),
                                  ),
                                  textAlign: TextAlign.center,
                                ),
                              ],
                            )
                            : const SizedBox.shrink(),
                  ),
                ),

                // Google Map with error handling
                if (!_hasMapError)
                  _isLoadingLocation
                      ? Center(
                        child: CircularProgressIndicator(
                          color: Theme.of(context).colorScheme.primary,
                        ),
                      )
                      : Builder(
                        builder: (context) {
                          try {
                            return Padding(
                              padding: const EdgeInsets.only(
                                bottom: 120,
                              ), // Account for draggable bottom sheet
                              child: GoogleMap(
                                initialCameraPosition: _initialCameraPosition,
                                markers: _markers,
                                myLocationEnabled: true, // Enable user location
                                myLocationButtonEnabled:
                                    false, // Disable to avoid overlap with bottom sheet
                                zoomControlsEnabled:
                                    false, // Disable to avoid overlap
                                onMapCreated: (controller) async {
                                  setState(() {
                                    _mapController = controller;
                                  });

                                  // If location was loaded before map creation, move camera
                                  if (!_isLoadingLocation) {
                                    try {
                                      Position position =
                                          await LocationService.getCachedOrCurrentLocation();
                                      controller.animateCamera(
                                        CameraUpdate.newLatLng(
                                          LatLng(
                                            position.latitude,
                                            position.longitude,
                                          ),
                                        ),
                                      );
                                    } catch (e) {
                                      // Ignore error, map will use initial position
                                    }
                                  }
                                },
                              ),
                            );
                          } catch (e) {
                            // Handle error
                            Future.microtask(() {
                              setState(() {
                                _hasMapError = true;
                              });
                            });
                            return Container(); // Return empty container if map fails
                          }
                        },
                      ),

                // Custom map controls
                Positioned(
                  right: 16,
                  bottom: 220, // Above the bottom sheet
                  child: Column(
                    children: [
                      // Location button
                      Container(
                        decoration: BoxDecoration(
                          color: Theme.of(context).scaffoldBackgroundColor,
                          borderRadius: BorderRadius.circular(8),
                          boxShadow: [
                            BoxShadow(
                              color: Colors.black.withValues(alpha: 0.1),
                              blurRadius: 4,
                              offset: const Offset(0, 2),
                            ),
                          ],
                        ),
                        child: IconButton(
                          icon: Icon(
                            Icons.my_location,
                            color: Theme.of(context).colorScheme.primary,
                          ),
                          onPressed: () async {
                            try {
                              Position position =
                                  await LocationService.getCachedOrCurrentLocation();
                              _mapController?.animateCamera(
                                CameraUpdate.newLatLngZoom(
                                  LatLng(position.latitude, position.longitude),
                                  15,
                                ),
                              );
                            } catch (e) {
                              // Handle error silently
                            }
                          },
                        ),
                      ),
                      const SizedBox(height: 8),
                      // Zoom controls
                      Container(
                        decoration: BoxDecoration(
                          color: Theme.of(context).scaffoldBackgroundColor,
                          borderRadius: BorderRadius.circular(8),
                          boxShadow: [
                            BoxShadow(
                              color: Colors.black.withValues(alpha: 0.1),
                              blurRadius: 4,
                              offset: const Offset(0, 2),
                            ),
                          ],
                        ),
                        child: Column(
                          children: [
                            IconButton(
                              icon: Icon(
                                Icons.add,
                                color: Theme.of(context).iconTheme.color,
                              ),
                              onPressed: () {
                                _mapController?.animateCamera(
                                  CameraUpdate.zoomIn(),
                                );
                              },
                            ),
                            Container(
                              height: 1,
                              color: Theme.of(
                                context,
                              ).colorScheme.outline.withValues(alpha: 0.2),
                            ),
                            IconButton(
                              icon: Icon(
                                Icons.remove,
                                color: Theme.of(context).iconTheme.color,
                              ),
                              onPressed: () {
                                _mapController?.animateCamera(
                                  CameraUpdate.zoomOut(),
                                );
                              },
                            ),
                          ],
                        ),
                      ),
                    ],
                  ),
                ),

                // Persistent bottom sheet with villa list
                Positioned(
                  left: 0,
                  right: 0,
                  bottom: 0,
                  child: _buildPersistentBottomSheet(),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }
}
